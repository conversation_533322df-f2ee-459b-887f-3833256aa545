# 🎬 Project Chimera Demo Preparation Guide

## 🎯 Demo Video Recording Checklist

### Pre-Recording Setup
- [ ] **Screen Recording Software Ready**
  - Windows: Windows Key + G (built-in)
  - macOS: Cmd+Shift+5 (built-in)
  - Cross-platform: OBS Studio or Loom
- [ ] **Audio Setup**
  - Microphone tested and clear
  - Quiet recording environment
  - No background noise
- [ ] **Application Setup**
  - Backend API running: `python -m uvicorn app.main:app`
  - Frontend UI running: `python demo_ui.py`
  - Both servers confirmed working

### Browser Tabs to Have Ready
1. **GitHub Repository**: https://github.com/hooiv/onlyfounders-hackathon-chimera
2. **Architecture Diagram**: `docs/architecture_diagram.md`
3. **Gradio Demo UI**: http://127.0.0.1:7860
4. **API Documentation**: http://127.0.0.1:8000/docs
5. **Code Editor**: Show `app/main.py` or `app/model.py`

## 🎭 De<PERSON> (2-3 Minutes)

### Part 1: Introduction & Architecture (0:00 - 0:45)

**[GitHub Repository Page]**
> "Hello, my name is <PERSON><PERSON><PERSON>, and this is my submission for the OnlyFounders AI Hackathon. I've built Project Chimera, an agent for Track 3: The Fundraise Prediction Agent."

> "My goal was to design an agent that is not only accurate but also modular, explainable, and, most importantly, privacy-preserving, in line with the core mission of OnlyFounders."

**[Architecture Diagram]**
> "Here is the architecture. My agent is designed to be the central brain in the AI swarm. It does not handle raw, sensitive data. Instead, it consumes pre-sanitized, numerical scores from other specialized agents, like a local Pitch Scorer or a ZK-powered Identity Agent."

> "The core logic, which includes an XGBoost model and a SHAP explainability module, is designed to run inside a Trusted Execution Environment, ensuring the entire prediction process is confidential and secure."

### Part 2: Live Demo (0:45 - 2:00)

**[Gradio UI - http://127.0.0.1:7860]**
> "Now, let's see it in action. This is an interactive demo of the Chimera Agent."

> "On the left, we have sliders representing the inputs from the other agents in the swarm: the Pitch Strength, Founder Identity, and Project Momentum scores."

**[Move sliders to show real-time updates]**

**[Scenario 1: Strong Project]**
> "Let's take the case of a strong project. It has a great pitch, a trustworthy founder, and good early momentum."

**[Set: Pitch: 8.5, Identity: 9.0, Momentum: 7.5]**
> "As you can see, the agent predicts that this project is 'Likely to Fund' with a high prediction score. Crucially, the explainability module tells us why: the key drivers for this decision were the high pitch strength and founder trust."

**[Scenario 2: Hyped but Risky]**
> "But what if a project has a lot of hype and momentum, but the pitch is weak and the founder's reputation is low?"

**[Set: Pitch: 3.0, Identity: 4.0, Momentum: 9.0]**
> "The agent correctly identifies this risk. The prediction is now 'Unlikely to Fund', and the key drivers clearly show that the low scores for pitch and identity were the most significant factors, despite the high momentum. This provides investors with a critical, nuanced signal."

### Part 3: Conclusion & Code (2:00 - 2:30)

**[Code Editor or API Docs]**
> "The entire application is built with a modular, production-ready stack using FastAPI for the backend and XGBoost for the model. The code is clean, well-documented, and ready for integration."

**[Optional: API Documentation]**
> "The API provides automatic documentation and is ready for platform integration."

> "Thank you for your time. Project Chimera demonstrates a realistic and secure approach to building the decentralized, AI-driven fundraising infrastructure that OnlyFounders is pioneering."

## 🎯 Key Demo Scenarios

### Scenario 1: High-Potential Startup
- **Pitch Strength**: 8.5
- **Identity Model**: 9.0
- **Momentum Tracker**: 7.5
- **Expected Result**: ~99% "Likely to Fund"
- **Key Drivers**: Pitch Strength, Identity Model

### Scenario 2: Hyped but Risky
- **Pitch Strength**: 3.0
- **Identity Model**: 4.0
- **Momentum Tracker**: 9.0
- **Expected Result**: Low confidence "Unlikely to Fund"
- **Key Drivers**: Low Pitch, Low Identity (despite high momentum)

### Scenario 3: Balanced Moderate
- **Pitch Strength**: 6.5
- **Identity Model**: 6.0
- **Momentum Tracker**: 5.5
- **Expected Result**: Moderate confidence
- **Key Drivers**: Balanced across features

## 🌟 Key Points to Emphasize

### Technical Excellence
1. **Real AI Intelligence**: XGBoost with 83% accuracy
2. **Explainable AI**: SHAP provides transparent reasoning
3. **Production Ready**: FastAPI with automatic documentation
4. **Privacy Preserving**: Only numerical scores, no raw data

### Innovation & Design
1. **Modular Architecture**: Clean separation of concerns
2. **TEE-Ready**: Designed for secure execution environments
3. **Integration Ready**: API-first design for platform integration
4. **Real-time**: Instant predictions and explanations

### User Experience
1. **Interactive Demo**: Beautiful Gradio interface
2. **Real-time Updates**: Predictions change as sliders move
3. **Clear Explanations**: SHAP key drivers for transparency
4. **Professional Design**: Clean, impressive presentation

## 📊 Demo Flow Tips

### Smooth Transitions
- Practice moving between browser tabs smoothly
- Have all URLs bookmarked and ready
- Test slider movements beforehand
- Prepare for any potential technical issues

### Storytelling Elements
- Start with the problem (privacy in fundraising)
- Show the solution (privacy-preserving agent swarm)
- Demonstrate the value (real-time, explainable predictions)
- End with the impact (secure, decentralized infrastructure)

### Technical Highlights
- Mention 83% model accuracy
- Emphasize SHAP explainability
- Highlight TEE-ready architecture
- Show API documentation quality

## 🎬 Post-Recording

### Video Editing
- Trim any mistakes at beginning/end
- Keep authentic feel (don't over-edit)
- Target 2-3 minutes total length
- Ensure audio is clear throughout

### Upload & Sharing
- Upload to YouTube (Unlisted) or Vimeo
- Get shareable link for README
- Test link accessibility
- Add to final submission materials

## ✅ Final Checklist

Before recording:
- [ ] Both servers running and tested
- [ ] All browser tabs prepared
- [ ] Demo scenarios tested
- [ ] Audio/video quality verified
- [ ] Script practiced at least once
- [ ] Backup plan for technical issues

After recording:
- [ ] Video edited and uploaded
- [ ] Shareable link obtained
- [ ] Link added to README
- [ ] Demo assets committed to GitHub

---

**🎯 Remember: You're showcasing a complete, professional AI application. Be confident and proud of what you've built!**
