# Project Chimera Demo Video Script

## Recording Checklist
- [ ] Screen recording software ready (Windows Key + G, OBS, or Loom)
- [ ] Microphone tested and clear
- [ ] Quiet recording environment
- [ ] Backend API server running (http://127.0.0.1:8000)
- [ ] Frontend Gradio UI running (http://127.0.0.1:7860)
- [ ] GitHub repository page open
- [ ] Architecture diagram ready to display

## Demo Script (2-3 Minutes)

### Part 1: Introduction & Architecture (0:00 - 0:45)

**[Start recording with GitHub repo page open]**

"Hello, my name is <PERSON><PERSON><PERSON>, and this is my submission for the OnlyFounders AI Hackathon. I've built Project Chimera, an agent for Track 3: The Fundraise Prediction Agent."

"My goal was to design an agent that is not only accurate but also modular, explainable, and, most importantly, privacy-preserving, in line with the core mission of OnlyFounders."

**[Switch to architecture diagram - docs/architecture_diagram.md]**

"Here is the architecture. My agent is designed to be the central brain in the AI swarm. It does not handle raw, sensitive data. Instead, it consumes pre-sanitized, numerical scores from other specialized agents, like a local Pitch Scorer or a ZK-powered Identity Agent."

"The core logic, which includes an XGBoost model and a SHAP explainability module, is designed to run inside a Trusted Execution Environment, ensuring the entire prediction process is confidential and secure."

### Part 2: The Live Demo (0:45 - 2:00)

**[Switch to running Gradio UI at http://127.0.0.1:7860]**

"Now, let's see it in action. This is an interactive demo of the Chimera Agent."

"On the left, we have sliders representing the inputs from the other agents in the swarm: the Pitch Strength, Founder Identity, and Project Momentum scores."

**[Move sliders to show real-time updates]**

"Let's take the case of a strong project. It has a great pitch, a trustworthy founder, and good early momentum."

**[Set sliders to: Pitch: 8.5, Identity: 9.0, Momentum: 7.5]**

"As you can see, the agent predicts that this project is 'Likely to Fund' with a high prediction score. Crucially, the explainability module tells us why: the key drivers for this decision were the high pitch strength and founder trust."

**[Demonstrate different scenario]**

"But what if a project has a lot of hype and momentum, but the pitch is weak and the founder's reputation is low?"

**[Set sliders to: Pitch: 3.0, Identity: 4.0, Momentum: 9.0]**

"The agent correctly identifies this risk. The prediction is now 'Unlikely to Fund', and the key drivers clearly show that the low scores for pitch and identity were the most significant factors, despite the high momentum. This provides investors with a critical, nuanced signal."

### Part 3: Conclusion & Code (2:00 - 2:30)

**[Briefly switch to code editor showing app/main.py or app/model.py]**

"The entire application is built with a modular, production-ready stack using FastAPI for the backend and XGBoost for the model. The code is clean, well-documented, and ready for integration."

**[Optional: Show API documentation at http://127.0.0.1:8000/docs]**

"Thank you for your time. Project Chimera demonstrates a realistic and secure approach to building the decentralized, AI-driven fundraising infrastructure that OnlyFounders is pioneering."

**[End recording]**

## Demo Scenarios to Showcase

### Scenario 1: High-Potential Startup
- Pitch Strength: 8.5
- Identity Model: 9.0  
- Momentum Tracker: 7.5
- Expected: ~99% "Likely to Fund"

### Scenario 2: Hyped but Risky
- Pitch Strength: 3.0
- Identity Model: 4.0
- Momentum Tracker: 9.0
- Expected: Lower confidence, "Unlikely to Fund"

### Scenario 3: Balanced Moderate
- Pitch Strength: 6.5
- Identity Model: 6.0
- Momentum Tracker: 5.5
- Expected: Moderate confidence

## Key Points to Emphasize

1. **Privacy-Preserving**: Only numerical scores, no raw data
2. **Explainable AI**: SHAP provides transparent reasoning
3. **Real-time**: Instant predictions as sliders move
4. **Production-Ready**: Clean architecture and documentation
5. **Integration-Ready**: API-first design for platform integration

## Technical Highlights

- XGBoost model with 83% accuracy
- SHAP explainability for transparency
- FastAPI backend with automatic documentation
- Gradio frontend for beautiful user experience
- TEE-ready architecture for secure execution
